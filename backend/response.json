{"customers": [{"id": 2237, "type": "REGULAR", "first_name": "test", "last_name": "test", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4726, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-22T19:26:45.778Z", "updated_at": "2025-07-22T19:43:57.584Z", "deleted_at": null, "company_name": "test", "language": null, "currency": "EUR", "companies": [{"id": 1239, "name": "test", "description": null, "vat": null, "tin": "testasyhcxnya1", "lucid": null, "customer_id": 2237, "starting": null, "website": null, "partner_id": null, "address_id": 1239, "created_at": "2025-07-22T19:43:38.239Z", "updated_at": "2025-07-22T19:43:38.239Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3767, "phone_number": "+49999-999", "customer_id": 2237, "created_at": "2025-07-22T19:43:52.568Z", "updated_at": "2025-07-22T19:43:52.568Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3768, "phone_number": "", "customer_id": 2237, "created_at": "2025-07-22T19:43:52.568Z", "updated_at": "2025-07-22T19:43:52.568Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "EU_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 4, "country_code": "FR", "country_name": "France", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/fr.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}]}, {"id": 2223, "type": "REGULAR", "first_name": "<PERSON>", "last_name": "Pignataro", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4712, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-21T14:17:41.109Z", "updated_at": "2025-07-21T14:19:11.500Z", "deleted_at": null, "company_name": "PignataroCompany", "language": null, "currency": "EUR", "companies": [{"id": 1236, "name": "PignataroCompany", "description": null, "vat": null, "tin": "UHEUHDEUH983948", "lucid": null, "customer_id": 2223, "starting": null, "website": null, "partner_id": null, "address_id": 1236, "created_at": "2025-07-21T14:19:07.846Z", "updated_at": "2025-07-21T14:19:07.846Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3761, "phone_number": "+49323 2323-3123", "customer_id": 2223, "created_at": "2025-07-21T14:19:11.363Z", "updated_at": "2025-07-21T14:19:11.363Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3762, "phone_number": "", "customer_id": 2223, "created_at": "2025-07-21T14:19:11.363Z", "updated_at": "2025-07-21T14:19:11.363Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "DIRECT_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 5, "country_code": "DE", "country_name": "Germany", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/de.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}]}, {"id": 2208, "type": "REGULAR", "first_name": "test", "last_name": "test", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4697, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-18T11:33:15.518Z", "updated_at": "2025-07-18T11:39:21.756Z", "deleted_at": null, "company_name": "test", "language": null, "currency": "EUR", "companies": [{"id": 1235, "name": "test", "description": null, "vat": null, "tin": "testas<PERSON><PERSON>", "lucid": "DE5755125739304", "customer_id": 2208, "starting": null, "website": null, "partner_id": null, "address_id": 1235, "created_at": "2025-07-18T11:39:17.516Z", "updated_at": "2025-07-23T15:36:30.948Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3759, "phone_number": "+49999-9999", "customer_id": 2208, "created_at": "2025-07-18T11:39:21.641Z", "updated_at": "2025-07-18T11:39:21.641Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3760, "phone_number": "", "customer_id": 2208, "created_at": "2025-07-18T11:39:21.641Z", "updated_at": "2025-07-18T11:39:21.641Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "EU_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}, {"country_id": 4, "country_code": "FR", "country_name": "France", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/fr.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}, {"type": "DIRECT_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 5, "country_code": "DE", "country_name": "Germany", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/de.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}, {"type": "ACTION_GUIDE", "status": "ACTIVE", "termination": null, "licenses": [], "action_guides": [{"country_id": 4, "country_code": "FR", "country_name": "France", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/fr.svg", "contract_status": "ACTIVE", "termination": null}]}]}, {"id": 2207, "type": "REGULAR", "first_name": "<PERSON>", "last_name": "<PERSON><PERSON>", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4696, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-18T03:24:34.543Z", "updated_at": "2025-07-18T03:31:08.807Z", "deleted_at": null, "company_name": "Empresa&Teste", "language": null, "currency": "EUR", "companies": [{"id": 1234, "name": "Empresa&Teste", "description": null, "vat": null, "tin": "000003", "lucid": null, "customer_id": 2207, "starting": null, "website": null, "partner_id": null, "address_id": 1234, "created_at": "2025-07-18T03:31:03.888Z", "updated_at": "2025-07-18T03:31:03.888Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3757, "phone_number": "+49000 00-000", "customer_id": 2207, "created_at": "2025-07-18T03:31:08.315Z", "updated_at": "2025-07-18T03:31:08.315Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3758, "phone_number": "+49000 00-000", "customer_id": 2207, "created_at": "2025-07-18T03:31:08.315Z", "updated_at": "2025-07-18T03:31:08.315Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "EU_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}]}, {"id": 2206, "type": "REGULAR", "first_name": "First", "last_name": "Surname", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4695, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-17T19:48:41.019Z", "updated_at": "2025-07-17T20:10:46.662Z", "deleted_at": null, "company_name": "test", "language": null, "currency": "EUR", "companies": [{"id": 1233, "name": "Company Name12", "description": null, "vat": null, "tin": "testahsycxhsd", "lucid": null, "customer_id": 2206, "starting": null, "website": null, "partner_id": null, "address_id": 1233, "created_at": "2025-07-17T19:52:45.290Z", "updated_at": "2025-07-17T20:13:48.061Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3755, "phone_number": "+49999-9999", "customer_id": 2206, "created_at": "2025-07-17T20:10:39.440Z", "updated_at": "2025-07-17T20:10:39.440Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3756, "phone_number": "", "customer_id": 2206, "created_at": "2025-07-17T20:10:39.440Z", "updated_at": "2025-07-17T20:10:39.440Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "EU_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}]}, {"id": 2204, "type": "REGULAR", "first_name": "yeh", "last_name": "eniy", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4693, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-17T14:29:43.881Z", "updated_at": "2025-07-17T14:38:56.968Z", "deleted_at": null, "company_name": "ythurim", "language": null, "currency": "EUR", "companies": [{"id": 1232, "name": "sucorazon", "description": null, "vat": null, "tin": "02020202", "lucid": null, "customer_id": 2204, "starting": null, "website": null, "partner_id": null, "address_id": 1232, "created_at": "2025-07-17T14:38:52.551Z", "updated_at": "2025-07-17T14:38:52.551Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3751, "phone_number": "+49000 00-000", "customer_id": 2204, "created_at": "2025-07-17T14:38:56.167Z", "updated_at": "2025-07-17T14:38:56.167Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3752, "phone_number": "+49000 00-000", "customer_id": 2204, "created_at": "2025-07-17T14:38:56.167Z", "updated_at": "2025-07-17T14:38:56.167Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "EU_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}]}, {"id": 2203, "type": "REGULAR", "first_name": "test", "last_name": "test", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4692, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-17T12:54:16.031Z", "updated_at": "2025-07-17T14:14:03.659Z", "deleted_at": null, "company_name": "test", "language": null, "currency": "EUR", "companies": [{"id": 1231, "name": "test", "description": null, "vat": null, "tin": "saydhcdmuj2", "lucid": null, "customer_id": 2203, "starting": null, "website": null, "partner_id": null, "address_id": 1231, "created_at": "2025-07-17T12:56:09.302Z", "updated_at": "2025-07-17T12:56:09.302Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3745, "phone_number": "+49999-999", "customer_id": 2203, "created_at": "2025-07-17T12:56:22.882Z", "updated_at": "2025-07-17T12:56:22.882Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3746, "phone_number": "", "customer_id": 2203, "created_at": "2025-07-17T12:56:22.882Z", "updated_at": "2025-07-17T12:56:22.882Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "DIRECT_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 5, "country_code": "DE", "country_name": "Germany", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/de.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}]}, {"id": 2184, "type": "REGULAR", "first_name": "test", "last_name": "test", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4673, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-16T18:41:01.655Z", "updated_at": "2025-07-16T18:45:06.825Z", "deleted_at": null, "company_name": "test", "language": null, "currency": "EUR", "companies": [{"id": 1230, "name": "test", "description": null, "vat": null, "tin": "ashcydh436", "lucid": null, "customer_id": 2184, "starting": null, "website": null, "partner_id": null, "address_id": 1230, "created_at": "2025-07-16T18:44:50.036Z", "updated_at": "2025-07-16T18:44:50.036Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3743, "phone_number": "+49999-999", "customer_id": 2184, "created_at": "2025-07-16T18:45:04.533Z", "updated_at": "2025-07-16T18:45:04.533Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3744, "phone_number": "", "customer_id": 2184, "created_at": "2025-07-16T18:45:04.533Z", "updated_at": "2025-07-16T18:45:04.533Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "EU_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}]}, {"id": 2183, "type": "REGULAR", "first_name": "Hgfhgfh", "last_name": "gfhgf", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4672, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-16T18:40:35.797Z", "updated_at": "2025-07-16T18:42:18.752Z", "deleted_at": null, "company_name": "tfhgfh", "language": null, "currency": "EUR", "companies": [{"id": 1229, "name": "tfhgfh", "description": null, "vat": null, "tin": "87565465", "lucid": null, "customer_id": 2183, "starting": null, "website": null, "partner_id": null, "address_id": 1229, "created_at": "2025-07-16T18:42:14.770Z", "updated_at": "2025-07-16T18:42:14.770Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3740, "phone_number": "+49879 84-65465", "customer_id": 2183, "created_at": "2025-07-16T18:42:18.674Z", "updated_at": "2025-07-16T18:42:18.674Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3741, "phone_number": "+49999 98-888", "customer_id": 2183, "created_at": "2025-07-16T18:42:18.674Z", "updated_at": "2025-07-16T18:42:18.674Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3742, "phone_number": "+49555 53-333", "customer_id": 2183, "created_at": "2025-07-16T18:42:18.674Z", "updated_at": "2025-07-16T18:42:18.674Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "EU_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 1, "country_code": "AT", "country_name": "Austria", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/at.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}, {"country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}]}, {"id": 2182, "type": "REGULAR", "first_name": "<PERSON>", "last_name": "Helena", "salutation": "Mr.", "email": "<EMAIL>", "user_id": 4671, "is_active": false, "document_id": null, "id_stripe": null, "created_at": "2025-07-16T18:30:02.671Z", "updated_at": "2025-07-16T18:33:36.719Z", "deleted_at": null, "company_name": "Caffe <PERSON>", "language": null, "currency": "EUR", "companies": [{"id": 1228, "name": "Caffe <PERSON>", "description": null, "vat": null, "tin": "************", "lucid": null, "customer_id": 2182, "starting": null, "website": null, "partner_id": null, "address_id": 1228, "created_at": "2025-07-16T18:33:32.768Z", "updated_at": "2025-07-16T18:33:32.768Z", "deleted_at": null, "industry_sector": null, "owner_name": null}], "phones": [{"id": 3738, "phone_number": "+49887 4654-9854", "customer_id": 2182, "created_at": "2025-07-16T18:33:36.693Z", "updated_at": "2025-07-16T18:33:36.693Z", "deleted_at": null, "phone_type": "PHONE"}, {"id": 3739, "phone_number": "", "customer_id": 2182, "created_at": "2025-07-16T18:33:36.693Z", "updated_at": "2025-07-16T18:33:36.693Z", "deleted_at": null, "phone_type": "PHONE"}], "contracts": [{"type": "EU_LICENSE", "status": "ACTIVE", "termination": null, "licenses": [{"country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}, {"country_id": 1, "country_code": "AT", "country_name": "Austria", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/at.svg", "year": 2025, "registration_status": "PENDING", "contract_status": "ACTIVE", "clerk_control_status": "PENDING", "termination": null}], "action_guides": []}, {"type": "ACTION_GUIDE", "status": "ACTIVE", "termination": null, "licenses": [], "action_guides": [{"country_id": 7, "country_code": "PT", "country_name": "Portugal", "country_flag": "https://cdn.kcak11.com/CountryFlags/countries/pt.svg", "contract_status": "ACTIVE", "termination": null}]}]}], "count": 828, "pages": 83, "current_page": 1}